import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseIntPipe,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { TransactionService } from './transaction.service';
import { CreateTransactionDto } from './dto/create.dto';
import { UpdateTransactionStatusDto } from './dto/update-status.dto';
import { TransactionFilterDto } from './dto/transaction-filter.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { User } from 'src/common/decorators/user.decorator';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import { FileInterceptor } from '@nestjs/platform-express';
import { DeclineTransactionDto } from './dto/decline.dto';
import { type TransactionStatus } from 'src/common/interfaces';
import { AcceptTransactionDto } from './dto/accept.dto';

@ApiTags('Transactions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('transactions')
export class TransactionController {
  constructor(private readonly transactionService: TransactionService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new transaction' })
  @ApiResponse({ status: 201, description: 'Transaction created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User or payment method not found' })
  @UseInterceptors(FileInterceptor('attachments'))
  async createTransaction(
    @Body() createTransactionDto: CreateTransactionDto,
    @User('id') userId: number,
    @UploadedFiles() attachments: Express.Multer.File[],
  ) {
    return this.transactionService.createTransaction(
      userId,
      createTransactionDto,
      attachments,
    );
  }

  @Get()
  @UseGuards(RolesGuard)
  @Roles('admin', 'super_admin')
  @ApiOperation({ summary: 'Get all transactions (Admin only)' })
  @ApiResponse({ status: 200, description: 'Returns list of all transactions' })
  async getAllTransactions(@Query() query: TransactionFilterDto) {
    return this.transactionService.getAllTransactions(query);
  }

  @Get('my-transactions')
  @ApiOperation({ summary: 'Get current user initiated transactions' })
  @ApiResponse({ status: 200, description: 'Returns user transactions' })
  async getUserTransactions(
    @User('id') userId: number,
    @Query() query: TransactionFilterDto,
  ) {
    return this.transactionService.getUserTransactions(userId, query);
  }

  @Get('received')
  @ApiOperation({ summary: 'Get transactions received by current user' })
  @ApiResponse({ status: 200, description: 'Returns received transactions' })
  async getReceivedTransactions(
    @User('id') userId: number,
    @Query() query: TransactionFilterDto,
  ) {
    return this.transactionService.getReceivedTransactions(userId, query);
  }

  @Post('decline')
  @ApiOperation({ summary: 'Decline a transaction' })
  @ApiResponse({
    status: 200,
    description: 'Transaction declined successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async declineTransaction(
    @Body() declineTransactionDto: DeclineTransactionDto,
    @User('id') userId: number,
  ) {
    return this.transactionService.declineTransaction(
      userId,
      declineTransactionDto,
    );
  }

  @Get('status-count')
  @ApiOperation({ summary: 'Get transaction status count' })
  @ApiResponse({ status: 200, description: 'Returns transaction status count' })
  async getStatusCount(@User('id') userId: number) {
    return await this.transactionService.getStatusCount(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get transaction by ID' })
  @ApiResponse({ status: 200, description: 'Returns transaction details' })
  @ApiResponse({ status: 404, description: 'Transaction not found' })
  async getTransactionById(@Param('id', ParseIntPipe) id: number) {
    return this.transactionService.getTransactionById(id);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update transaction status' })
  @ApiResponse({ status: 200, description: 'Transaction status updated' })
  @ApiResponse({ status: 404, description: 'Transaction not found' })
  async updateTransactionStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateStatusDto: UpdateTransactionStatusDto,
    @User('id') userId: number,
  ) {
    return this.transactionService.updateTransactionStatus(
      id,
      updateStatusDto.status,
      userId,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete transaction (Initiator only)' })
  @ApiResponse({ status: 200, description: 'Transaction deleted successfully' })
  @ApiResponse({ status: 400, description: 'Cannot delete transaction' })
  @ApiResponse({ status: 404, description: 'Transaction not found' })
  async deleteTransaction(
    @Param('id', ParseIntPipe) id: number,
    @User('id') userId: number,
  ) {
    return this.transactionService.deleteTransaction(id, userId);
  }

  @Get('payment-method/:id')
  @ApiOperation({ summary: 'Get transactions by payment method' })
  @ApiResponse({ status: 200, description: 'Returns transactions' })
  async getTransactionsByPaymentMethod(
    @Param('id', ParseIntPipe) id: number,
    @User('id') userId: number,
    @Query() query: TransactionFilterDto,
  ) {
    return this.transactionService.getTransactionsByPaymentMethod(
      id,
      query,
      userId,
    );
  }

  @Get('category/:category')
  @ApiOperation({ summary: 'Get transactions by category' })
  @ApiResponse({ status: 200, description: 'Returns transactions' })
  async getTransactionsByCategory(
    @Param('category') category: string,
    @Query() query: TransactionFilterDto,
  ) {
    return this.transactionService.getTransactionsByCategory(category, query);
  }

  @Get('status/:status')
  @ApiOperation({ summary: 'Get transactions by status' })
  @ApiResponse({ status: 200, description: 'Returns transactions' })
  async getTransactionsByStatus(
    @Param('status') status: TransactionStatus,
    @Query() query: TransactionFilterDto,
  ) {
    return this.transactionService.getTransactionsByStatus(status, query);
  }

  @Get('amount-range/:minAmount/:maxAmount')
  @ApiOperation({ summary: 'Get transactions by amount range' })
  @ApiResponse({ status: 200, description: 'Returns transactions' })
  async getTransactionsByAmountRange(
    @Param('minAmount', ParseIntPipe) minAmount: number,
    @Param('maxAmount', ParseIntPipe) maxAmount: number,
    @Query() query: TransactionFilterDto,
  ) {
    return this.transactionService.getTransactionsByAmountRange(
      minAmount,
      maxAmount,
      query,
    );
  }

  @Get('search/:searchTerm')
  @ApiOperation({ summary: 'Search transactions' })
  @ApiResponse({ status: 200, description: 'Returns transactions' })
  async searchTransactions(
    @Param('searchTerm') searchTerm: string,
    @Query() query: TransactionFilterDto,
  ) {
    return this.transactionService.searchTransactions(searchTerm, query);
  }
  @Post('accept')
  @ApiOperation({ summary: 'Accept a transaction' })
  @ApiResponse({
    status: 200,
    description: 'Transaction accepted successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Transaction not found' })
  async acceptTransaction(
    @User('id') userId: number,
    @Body() acceptTransactionDto: AcceptTransactionDto,
  ) {
    return this.transactionService.acceptTransaction(
      userId,
      acceptTransactionDto,
    );
  }

  @Get('date-range/:startDate/:endDate')
  @ApiOperation({ summary: 'Get transactions by date range' })
  @ApiResponse({ status: 200, description: 'Returns transactions' })
  async getTransactionsByDateRange(
    @Param('startDate') startDate: string,
    @Param('endDate') endDate: string,
    @Query() query: TransactionFilterDto,
  ) {
    return this.transactionService.getTransactionsByDateRange(
      startDate,
      endDate,
      query,
    );
  }
}
