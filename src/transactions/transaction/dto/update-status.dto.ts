import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import * as interfaces from 'src/common/interfaces';

export class UpdateTransactionStatusDto {
  @ApiProperty({
    example: 'completed',
    description: 'Transaction status',
    enum: ['completed', 'disputed', 'cancelled', 'expired', 'failed'],
    required: true,
  })
  @IsEnum(['completed', 'disputed', 'cancelled', 'expired', 'failed'])
  @IsNotEmpty()
  status: interfaces.TransactionStatus;
}
