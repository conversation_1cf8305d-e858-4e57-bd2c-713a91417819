import { Module } from '@nestjs/common';
import { TransactionController } from './transaction.controller';
import { TransactionService } from './transaction.service';
import {
  TransactionRepository,
  UserRepository,
  PaymentMethodRepository,
  PaymentRepository,
} from 'src/common/repository';
import { EmailsService } from 'src/core/emails/emails.service';
import { NotificationsService } from 'src/core/notifications/notifications.service';
import { NotificationRepository } from 'src/common/repository';
import { FilesService } from 'src/core/files/files.service';
import { FileRepository } from 'src/common/repository';
import { EscrowRepository } from 'src/common/repository/escrow.repository';
import { PaymentModule } from 'src/core/payment/payment.module';
@Module({
  imports: [PaymentModule],
  controllers: [TransactionController],
  providers: [
    TransactionService,
    TransactionRepository,
    UserRepository,
    PaymentMethodRepository,
    PaymentRepository,

    EmailsService,
    NotificationsService,
    FilesService,
    EscrowRepository,
    NotificationRepository,
    FileRepository,
  ],
})
export class TransactionModule {}
