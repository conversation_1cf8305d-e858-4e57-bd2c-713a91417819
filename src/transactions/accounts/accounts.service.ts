import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateMobileMoneyAccountDto } from './dto/create-momo-account.dto';
import { PaymentMethodRepository, UserRepository } from 'src/common/repository';
import { EmailsService } from 'src/core/emails/emails.service';

@Injectable()
export class AccountsService {
  constructor(
    private readonly paymentMethodRepository: PaymentMethodRepository,
    private readonly userRepository: UserRepository,
    private readonly emailService: EmailsService,
  ) {}
  async linkMtnAccount(
    userId: number,
    createAccountDto: CreateMobileMoneyAccountDto,
  ) {
    const user = await this.userRepository.findById(userId);
    if (!user) throw new NotFoundException('User not found');

    const paymentMethod =
      await this.paymentMethodRepository.createMtnPaymentMethod(userId, {
        type: 'mobile_money',
        accountName: createAccountDto.accountName,
        countryCode: createAccountDto.countryCode,
        accountNumber: createAccountDto.accountNumber,
        provider: createAccountDto.provider,
        name: 'MTN Mobile Money',
      });

    await this.emailService.sendCustomEmail(
      user.email,
      user.name,
      'Mobile Money Account Verification Required',
      `Your mobile money account has been linked to your account. Please verify your account by calling the number below. <br> <strong>Phone Number: ${createAccountDto.accountNumber}</strong>`,
    );

    return paymentMethod;
  }

  async linkOrangeAccount(
    userId: number,
    createAccountDto: CreateMobileMoneyAccountDto,
  ) {
    const user = await this.userRepository.findById(userId);
    if (!user) throw new NotFoundException('User not found');

    const paymentMethod =
      await this.paymentMethodRepository.createOrangePaymentMethod(userId, {
        type: 'mobile_money',
        accountName: createAccountDto.accountName,
        countryCode: createAccountDto.countryCode,
        accountNumber: createAccountDto.accountNumber,
        provider: createAccountDto.provider,
        name: 'Orange Money',
      });
    await this.emailService.sendCustomEmail(
      user.email,
      user.name,
      'Mobile Money Account Verification Required',
      `Your mobile money account has been linked to your account. Please verify your account by calling the number below. <br> <strong>Phone Number: ${createAccountDto.accountNumber}</strong>`,
    );

    return paymentMethod;
  }

  async findAll(userId: number) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');
      return this.paymentMethodRepository.getAllUserPaymentMethods(userId);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async findOne(id: number) {
    try {
      const paymentMethod =
        await this.paymentMethodRepository.getPaymentMethodById(id);
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      return paymentMethod;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async setAsDefault(id: number) {
    try {
      const paymentMethod =
        await this.paymentMethodRepository.getPaymentMethodById(id);
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      await this.paymentMethodRepository.update(id, { idDefault: true });
      return paymentMethod;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
  async activatePaymentMethod(id: number, userId: number) {
    try {
      const paymentMethod =
        await this.paymentMethodRepository.getPaymentMethodById(id);
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      if (paymentMethod.userId !== userId)
        throw new Error('You are not allowed to activate this account');

      await this.paymentMethodRepository.update(id, { isActive: true });
      return paymentMethod;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async deactivatePaymentMethod(id: number, userId: number) {
    try {
      const paymentMethod =
        await this.paymentMethodRepository.getPaymentMethodById(id);
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      if (paymentMethod.userId !== userId)
        throw new Error('You are not allowed to deactivate this account');

      await this.paymentMethodRepository.update(id, { isActive: false });
      return paymentMethod;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async remove(id: number, userId: number) {
    const paymentMethod =
      await this.paymentMethodRepository.getPaymentMethodById(id);
    if (!paymentMethod) throw new NotFoundException('Payment method not found');
    if (paymentMethod.userId !== userId)
      throw new Error('You are not allowed to delete this account');
    return this.paymentMethodRepository.delete(id);
  }
}
