import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreateMobileMoneyAccountDto {
  @ApiProperty({ example: '+************', description: 'Account number' })
  @IsString()
  @IsNotEmpty()
  accountNumber: string;

  @ApiProperty({ example: '<PERSON>', description: 'Account holder name' })
  @IsString()
  @IsNotEmpty()
  accountName: string;

  @ApiProperty({ example: 'MTN', description: 'Payment provider' })
  @IsString()
  @IsNotEmpty()
  provider: string;

  @ApiProperty({ example: 'CM', description: 'Country code' })
  @IsString()
  @IsNotEmpty()
  countryCode: string;
}
