import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { EscrowRepository } from 'src/common/repository/escrow.repository';
import { PaginationQuery } from 'src/common/interfaces';

@Injectable()
export class EscrowService {
  constructor(private readonly escrowRepository: EscrowRepository) {}

  async getById(id: number) {
    try {
      const escrow = await this.escrowRepository.findById(id);
      if (!escrow) throw new NotFoundException('Escrow not found');
      return escrow;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getAll(query: PaginationQuery) {
    try {
      return await this.escrowRepository.findAll(query);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
  async getStats() {
    try {
      return await this.escrowRepository.stats();
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getIncome() {
    try {
      return await this.escrowRepository.income();
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
  async getByTransactionId(transactionId: number) {
    try {
      const escrow =
        await this.escrowRepository.findByTransactionId(transactionId);
      if (!escrow) throw new NotFoundException('Escrow not found');
      return escrow;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
