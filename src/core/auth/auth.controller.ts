/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import {
  Controller,
  UseGuards,
  Body,
  Post,
  Get,
  Res,
  Req,
} from '@nestjs/common';
import type { Request, Response } from 'express';
import { AuthService } from './auth.service';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { LoginDto } from './dto/login.dto';
import { SignUpDto } from './dto/signup.dto';
import { EmailDto } from './dto/email.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { TokenDto } from './dto/token.dto';
import { LocalAuthGuard } from 'src/common/guards/local.guard';
import { AuthGuard } from '@nestjs/passport';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}
  @Get('google')
  @UseGuards(AuthGuard('google'))
  async googleAuth() {
    // Initiates Google OAuth login
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  googleAuthCallback(@Req() req: Request, @Res() res: Response) {
    const user = req.user as any;

    const tokenResponse = this.authService.login({
      id: user.id,
      email: user.email,
      role: user.role,
      isVerified: user.verified,
    });

    // email
    // if (!user.isVerified) {
    //   this.authService.({ email: user.email });
    // }

    res.redirect(
      `${process.env.FRONTEND_URL}/auth/success?token=${tokenResponse.access_token}`,
    );
  }

  @Post('google/mobile')
  async googleMobileAuth(@Body('token') token: string) {
    try {
      const user = await this.authService.validateGoogleToken(token);
      if (!user) return { success: false, message: 'Invalid Google token' };
      const jwtToken = this.authService.login(user);
      return { success: true, user, token: jwtToken };
    } catch {
      return { success: false, message: 'Invalid Google token' };
    }
  }

  @Post('login')
  @UseGuards(LocalAuthGuard)
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({ status: 200, description: 'Returns access token' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async login(@Body() dto: LoginDto) {
    return this.authService.validateUser(dto);
  }

  @Post('signup')
  @ApiOperation({ summary: 'Register new user' })
  @ApiResponse({ status: 201, description: 'Returns access token' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async signup(@Body() dto: SignUpDto) {
    return this.authService.signUp(dto);
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async forgotPassword(@Body() dto: EmailDto) {
    return this.authService.forgotPassword(dto);
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({ status: 200, description: 'Password updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid token' })
  async resetPassword(@Body() dto: ResetPasswordDto) {
    return this.authService.resetPassword(dto);
  }

  @Post('test-email')
  async testEmail() {
    return this.authService.testEmail();
  }

  @Post('verify')
  @ApiOperation({ summary: 'Verify user account' })
  @ApiResponse({ status: 200, description: 'Account verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid verification token' })
  async verifyAccount(@Body() dto: TokenDto) {
    return this.authService.verifyUsersAccount(dto);
  }

  @Post('send-verification')
  @ApiOperation({ summary: 'Send verification token' })
  @ApiResponse({ status: 200, description: 'Verification token sent' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async sendVerification(@Body() dto: EmailDto) {
    return this.authService.sendVerificationToken(dto);
  }
}
