import { Modu<PERSON> } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import {
  ProfileRepository,
  UserRepository,
  TokenRepository,
} from 'src/common/repository';
import { JwtStrategy } from './strategy/jwt.stratetegy';
import { LocalStrategy } from './strategy/local.strategy';
import { GoogleStrategy } from './strategy/google.strategy';

@Module({
  imports: [
    PassportModule,
    JwtModule.registerAsync({
      useFactory: (config: ConfigService) => ({
        secret: config.get('JWT_SECRET'),
        signOptions: { expiresIn: '7d' },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    TokenRepository,
    UserRepository,
    ProfileRepository,
    JwtStrategy,
    LocalStrategy,
    GoogleStrategy,
  ],
})
export class AuthModule {}
