import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  PaymentMethodRepository,
  PaymentRepository,
  TransactionRepository,
} from 'src/common/repository';
import { NewPayment } from 'src/common/schemas';

@Injectable()
export class PaymentService {
  constructor(
    private readonly paymentRepository: PaymentRepository,
    private readonly paymentMethodRepository: PaymentMethodRepository,
    private readonly transactionRepository: TransactionRepository,
  ) {}

  async createPayment(paymentData: NewPayment) {
    try {
      if (!paymentData.transactionId || !paymentData.paymentMethodId)
        throw new BadRequestException('Transaction ID is required');
      const paymentMethod = await this.paymentMethodRepository.findById(
        paymentData.paymentMethodId,
      );
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');
      const transaction = await this.transactionRepository.findById(
        paymentData.transactionId,
      );
      if (!transaction) throw new NotFoundException('Transaction not found');
      if (!paymentMethod.isActive)
        throw new BadRequestException('Payment method is not active');
      // based on the payment method type, call the appropriate payment gateway
      // for now, we will just create a payment record
      const payment = await this.paymentRepository.create(paymentData);

      if (paymentMethod.type === 'mobile_money') {
        // call mtn api
      }

      if (paymentMethod.type === 'master_card') {
        // call master card api
      }

      if (paymentMethod.type === 'visa_card') {
        // call visa card api
      }

      // notify all parties
      return payment;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getPaymentMethods(transactionId: number) {
    try {
      const payments =
        await this.paymentRepository.findByTransactionId(transactionId);
      return payments;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
