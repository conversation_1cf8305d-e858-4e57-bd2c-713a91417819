import { Module } from '@nestjs/common';
import {
  NotificationRepository,
  PaymentMethodRepository,
  PaymentRepository,
  TransactionRepository,
  UserRepository,
} from 'src/common/repository';
import { TransactionEntity } from 'src/transactions/transaction/entity/tranaction';
import { NotificationsService } from '../notifications/notifications.service';
import { PaymentService } from './payment.service';

@Module({
  providers: [
    PaymentRepository,
    PaymentMethodRepository,
    TransactionRepository,
    TransactionEntity,
    NotificationsService,
    NotificationRepository,
    UserRepository,
    PaymentService,
  ],
  exports: [PaymentService],
})
export class PaymentModule {}
