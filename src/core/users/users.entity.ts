import { User } from 'src/common/schemas/users';
import { ApiProperty } from '@nestjs/swagger';
export class UserProfileEntity {
  @ApiProperty({ description: 'User ID' })
  id: number;

  @ApiProperty({ description: 'User first name' })
  firstName: string;

  @ApiProperty({ description: 'User last name' })
  lastName: string;

  @ApiProperty({ description: 'User code' })
  code: string;

  @ApiProperty({ description: 'User bio' })
  bio: string;

  @ApiProperty({ description: 'User avatar' })
  avatar: string;

  constructor(data: Partial<UserProfileEntity>) {
    Object.assign(this, data);
  }
}

export class UserEntity implements User {
  @ApiProperty({
    description: 'User role',
    enum: ['admin', 'user', 'super_admin'],
  })
  role: 'admin' | 'user' | 'super_admin';

  @ApiProperty({ description: 'Google ID for OAuth' })
  googleId: string;

  @ApiProperty({ description: 'User active status' })
  isActive: boolean;

  @ApiProperty({ description: 'User verification status' })
  isVerified: boolean;

  @ApiProperty({ description: 'User deletion status' })
  isDeleted: boolean;

  @ApiProperty({ description: 'User ban status' })
  isBanned: boolean;

  @ApiProperty({ description: 'Ban timestamp' })
  bannedAt: Date;

  @ApiProperty({ description: 'Deletion timestamp' })
  deletedAt: Date;

  @ApiProperty({ description: 'Notification token' })
  notificationToken: string;

  @ApiProperty({ description: 'Verification timestamp' })
  verifiedAt: Date;

  @ApiProperty({ description: 'Hashed password' })
  password: string;

  @ApiProperty({ description: 'Authentication provider' })
  provider: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;

  @ApiProperty({ description: 'User ID' })
  id: number;

  @ApiProperty({ description: 'User full name' })
  name: string;

  @ApiProperty({ description: 'User email address' })
  email: string;

  @ApiProperty({ description: 'User profile information' })
  profile?: UserProfileEntity;

  constructor(data: Partial<UserEntity>) {
    Object.assign(this, data);
  }
}
