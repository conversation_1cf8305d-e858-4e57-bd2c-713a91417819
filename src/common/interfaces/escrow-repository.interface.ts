import { Escrow, Transaction } from '../schemas';
import { PaginatedResponse, PaginationQuery } from './paginaton';

export interface IEscrowRepository {
  findByTransactionId(
    transactionId: number,
  ): Promise<{ escrow: Escrow; transaction: Transaction | null } | null>;
  create(escrowData: Escrow): Promise<Escrow>;
  findAll(query: PaginationQuery): Promise<PaginatedResponse<any>>;
  update(id: number, escrowData: Partial<Escrow>): Promise<Escrow | null>;
  delete(id: number): Promise<boolean>;
  findById(
    id: number,
  ): Promise<{ escrow: Escrow; transaction: Transaction | null } | null>;
  stats(): Promise<{
    fialed: number;
    successful: number;
    pending: number;
    total: number;
    disputed: number;
    cancelled: number;
    expired: number;
  }>;
  income(): Promise<{ total: number; average: number }>;
}
