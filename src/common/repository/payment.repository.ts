import { Injectable, Inject } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { payments, Payment } from '../schemas';
import * as schema from '../schemas/payment';
import { IPaymentRepository } from '../interfaces/payment-repistory.interface';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

@Injectable()
export class PaymentRepository implements IPaymentRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async create(paymentData: schema.NewPayment): Promise<Payment> {
    const result = await this.db
      .insert(payments)
      .values({ ...paymentData })
      .returning();

    return result[0];
  }

  async findByTransactionId(transactionId: number): Promise<Payment[] | null> {
    const result = await this.db
      .select()
      .from(payments)
      .where(eq(payments.transactionId, transactionId));

    return result || null;
  }

  async update(
    id: number,
    paymentData: Partial<Payment>,
  ): Promise<Payment | null> {
    const result = await this.db
      .update(payments)
      .set({
        ...paymentData,
        updatedAt: new Date(),
      })
      .where(eq(payments.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(payments)
      .where(eq(payments.id, id))
      .returning();

    return result.length > 0;
  }
}
