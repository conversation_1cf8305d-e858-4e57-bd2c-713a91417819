import { Injectable, Inject } from '@nestjs/common';
import { eq, desc, count, and, like, or, asc, SQL } from 'drizzle-orm';
import {
  transactions,
  Transaction,
  NewTransaction,
  escrow,
  files,
  users,
  User,
  Escrow,
  File,
} from '../schemas';
import * as schema from '../schemas/transaction';
import { ITransactionRepository } from '../interfaces';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PaginatedResponse, TransactionFilter } from '../interfaces';
import { alias } from 'drizzle-orm/pg-core';

@Injectable()
export class TransactionRepository implements ITransactionRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async findAll(query: TransactionFilter): Promise<PaginatedResponse<any>> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');

    // Build where conditions
    const whereConditions = this.buildWhereConditions(query);

    // Count total items with filters
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions)
      .where(whereConditions);

    // Build order by clause
    const orderBy = this.buildOrderBy(query);

    const data = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
      })
      .from(transactions)
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .where(whereConditions)
      .limit(limit)
      .offset(offset)
      .orderBy(orderBy);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async create(transactionData: NewTransaction): Promise<Transaction> {
    const result = await this.db
      .insert(transactions)
      .values({
        ...transactionData,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async update(
    id: number,
    transactionData: Partial<Transaction>,
  ): Promise<Transaction | null> {
    const result = await this.db
      .update(transactions)
      .set({
        ...transactionData,
        updatedAt: new Date(),
      })
      .where(eq(transactions.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(transactions)
      .where(eq(transactions.id, id))
      .returning();

    return result.length > 0;
  }

  async findById(id: number): Promise<Transaction | null> {
    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.id, id))
      .limit(1);

    return result[0] || null;
  }

  async findByIdWithUsers(id: number): Promise<{
    transaction: Transaction;
    initiatedByUser: User;
    receivedByUser: User;
    files: File[];
    escrow: Escrow;
  } | null> {
    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');

    // First get the transaction with users and escrow
    const transactionResult = await this.db
      .select({
        transaction: transactions,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
        escrow: escrow,
      })
      .from(transactions)
      .where(eq(transactions.id, id))
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .limit(1);

    if (!transactionResult[0]) {
      return null;
    }

    // Get all files for this transaction
    const filesResult = await this.db
      .select()
      .from(files)
      .where(eq(files.transactionId, id));

    const result = transactionResult[0];

    return {
      transaction: result.transaction,
      initiatedByUser: result.initiatedByUser!,
      receivedByUser: result.receivedByUser!,
      files: filesResult,
      escrow: result.escrow!,
    };
  }

  async findByUserId(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const baseConditions = this.buildWhereConditions(query);
    const userCondition = eq(transactions.initiatedBy, userId);
    const whereConditions = baseConditions
      ? and(userCondition, baseConditions)
      : userCondition;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions)
      .where(whereConditions);

    const orderBy = this.buildOrderBy(query);

    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');

    const result = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
      })
      .from(transactions)
      .where(whereConditions)
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .limit(limit)
      .offset(offset)
      .orderBy(orderBy);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByReceivedBy(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const baseConditions = this.buildWhereConditions(query);
    const userCondition = eq(transactions.receivedBy, userId);
    const whereConditions = baseConditions
      ? and(userCondition, baseConditions)
      : userCondition;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions)
      .where(whereConditions);

    const orderBy = this.buildOrderBy(query);

    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');

    const result = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
      })
      .from(transactions)
      .where(whereConditions)
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .limit(limit)
      .offset(offset)
      .orderBy(orderBy);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByEscrowId(escrowId: number): Promise<Transaction | null> {
    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.id, escrowId))
      .limit(1);

    return result[0] || null;
  }

  async findByPaymentMethodId(
    paymentMethodId: number,
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions);
    const initiatedByUser = alias(users, 'initiatedByUser');
    const receivedByUser = alias(users, 'receivedByUser');
    const result = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
        initiatedByUser: initiatedByUser,
        receivedByUser: receivedByUser,
      })
      .from(transactions)
      .where(
        and(
          eq(transactions.paymentMethodId, paymentMethodId),
          eq(transactions.initiatedBy, userId),
        ),
      )
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .leftJoin(
        initiatedByUser,
        eq(transactions.initiatedBy, initiatedByUser.id),
      )
      .leftJoin(receivedByUser, eq(transactions.receivedBy, receivedByUser.id))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(transactions.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  private buildWhereConditions(
    query: TransactionFilter,
  ): SQL<unknown> | undefined {
    const conditions: SQL<unknown>[] = [];

    if (query.status) conditions.push(eq(transactions.status, query.status));

    if (query.search) {
      conditions.push(
        or(
          like(transactions.title, `%${query.search}%`),
          like(transactions.description, `%${query.search}%`),
        )!,
      );
    }

    return conditions.length > 0 ? and(...conditions) : undefined;
  }

  private buildOrderBy(query: TransactionFilter): SQL<unknown> {
    const sortBy = query.sortBy || 'createdAt';
    const sortOrder = query.sortOrder || 'desc';

    const column = {
      createdAt: transactions.createdAt,
      updatedAt: transactions.updatedAt,
      amount: transactions.amount,
      deadLine: transactions.deadLine,
      title: transactions.title,
    }[sortBy];

    return sortOrder === 'asc' ? asc(column) : desc(column);
  }
  async getStatusCount(
    userId: number,
  ): Promise<[{ status: string; count: number }]> {
    const result = await this.db
      .select({
        status: transactions.status,
        count: count(),
      })
      .from(transactions)
      .where(eq(transactions.initiatedBy, userId))
      .groupBy(transactions.status);

    return result as [{ status: string; count: number }];
  }
}
