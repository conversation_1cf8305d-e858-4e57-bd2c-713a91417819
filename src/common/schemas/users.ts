import {
  pgTable,
  serial,
  varchar,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { kyc } from './kyc';
import { relations } from 'drizzle-orm';
import { transactions } from './transaction';
import { profiles } from './profile';
import { paymentMethods } from './payment-method';
import { pgEnum } from 'drizzle-orm/pg-core';
import { tokens } from './token';
import { notifications } from './notification';
import { devices } from './devices';

export const roleEnum = pgEnum('role', ['admin', 'user', 'super_admin']);

export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  role: roleEnum('role').default('user'),
  googleId: varchar('google_id', { length: 255 }),
  isActive: boolean('is_active').notNull().default(true),
  isVerified: boolean('is_verified').notNull().default(false),
  isDeleted: boolean('is_deleted').notNull().default(false),
  isBanned: boolean('is_banned').notNull().default(false),
  bannedAt: timestamp('banned_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at').notNull().defaultNow(),
  notificationToken: varchar('notification_token', { length: 255 }),
  verifiedAt: timestamp('verified_at').notNull().defaultNow(),
  password: varchar('password', { length: 255 }),
  provider: varchar('provider', { length: 255 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export const usersRelation = relations(users, ({ one, many }) => ({
  kyc: one(kyc),
  transactions: many(transactions),
  profile: one(profiles),
  paymentMethod: many(paymentMethods),
  tokens: many(tokens),
  notifications: many(notifications),
  devices: many(devices),
}));
