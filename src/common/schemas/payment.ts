import { relations } from 'drizzle-orm';
import {
  pgTable,
  serial,
  varchar,
  timestamp,
  integer,
} from 'drizzle-orm/pg-core';
import { pgEnum } from 'drizzle-orm/pg-core';
import { transactions } from './transaction';
import { paymentMethods } from './payment-method';

export const paymentStatusEnum = pgEnum('payment_status', [
  'pending',
  'completed',
  'failed',
  'refunded',
  'cancelled',
]);

export const paymentDirection = pgEnum('payment_direction', [
  'to_escrow',
  'to_reciver',
  'from_escrow',
]);

export const payments = pgTable('payments', {
  id: serial('id').primaryKey(),
  amount: integer('amount').notNull(),
  paymentId: varchar('payment_id', { length: 255 }),
  currency: varchar('currency', { length: 255 }),
  status: paymentStatusEnum('status').notNull(),
  direction: paymentDirection('direction').notNull(),
  transactionId: integer('transaction_id').references(() => transactions.id),
  paymentMethodId: integer('payment_method_id').references(
    () => paymentMethods.id,
  ),
  reference: varchar('reference', { length: 255 }),
  description: varchar('description', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const paymentRelations = relations(payments, ({ one }) => ({
  transaction: one(transactions, {
    fields: [payments.transactionId],
    references: [transactions.id],
  }),
  paymentMethod: one(paymentMethods, {
    fields: [payments.paymentMethodId],
    references: [paymentMethods.id],
  }),
}));

export type Payment = typeof payments.$inferSelect;
export type NewPayment = typeof payments.$inferInsert;
